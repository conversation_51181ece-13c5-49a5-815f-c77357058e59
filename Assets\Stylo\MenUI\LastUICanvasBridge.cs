using UnityEngine;
using UnityEngine.InputSystem;

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple bridge component for Last UI canvas to communicate back to MenUI.
    /// <PERSON>les input for returning to the pause menu from settings.
    /// </summary>
    public class LastUICanvasBridge : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool exitOnMainMenuOnly = true;

        // Input system
        private DefaultControls _controls;
        private InputAction _cancelAction;

        // Components
        private StateManager _stateManager;
        private SimpleMenUISystem _menUISystem;

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeInput();
            FindComponents();
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                _cancelAction.performed += OnCancelInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                _cancelAction.performed -= OnCancelInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        #endregion

        #region Initialization

        private void InitializeInput()
        {
            _controls = new DefaultControls();
            _cancelAction = _controls.UI.Cancel;

            if (enableDebugLogging)
                Debug.Log("[LastUICanvasBridge] Input system initialized");
        }

        private void FindComponents()
        {
            // Find StateManager in this canvas or parent
            _stateManager = GetComponentInChildren<StateManager>();
            if (_stateManager == null)
            {
                _stateManager = GetComponentInParent<StateManager>();
            }

            if (_stateManager == null)
            {
                Debug.LogWarning("[LastUICanvasBridge] StateManager not found in Last UI canvas hierarchy");
            }

            // Find SimpleMenUISystem in the scene
            _menUISystem = FindFirstObjectByType<SimpleMenUISystem>();
            if (_menUISystem == null)
            {
                Debug.LogError("[LastUICanvasBridge] SimpleMenUISystem not found in scene");
            }

            if (enableDebugLogging)
                Debug.Log($"[LastUICanvasBridge] Components found - StateManager: {(_stateManager != null)}, MenUISystem: {(_menUISystem != null)}");
        }

        #endregion

        #region Input Handling

        private void OnCancelInput(InputAction.CallbackContext context)
        {
            if (enableDebugLogging)
                Debug.Log("[LastUICanvasBridge] Cancel input detected");

            if (ShouldExitToMenUI())
            {
                RequestExitToMenUI();
            }
            else if (_stateManager != null)
            {
                // Let Last UI handle the back navigation
                StartCoroutine(_stateManager.PlayPreviousCanvasAnimation());
            }
        }

        #endregion

        #region Navigation Logic

        /// <summary>
        /// Determine if we should exit to MenUI or handle navigation within Last UI
        /// </summary>
        private bool ShouldExitToMenUI()
        {
            if (_stateManager == null)
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUICanvasBridge] StateManager not found, defaulting to exit");
                return true;
            }

            if (exitOnMainMenuOnly)
            {
                // Only exit if we're on the main settings menu
                var activeCanvas = _stateManager.ActiveCanvas;
                if (activeCanvas != null && activeCanvas.canvasType != null)
                {
                    // Check if this is the main settings canvas (usually the first one)
                    bool isMainMenu = activeCanvas.canvasType == _stateManager.FirstCanvas;

                    if (enableDebugLogging)
                        Debug.Log($"[LastUICanvasBridge] Current canvas: {activeCanvas.canvasType.name}, Is main menu: {isMainMenu}");

                    return isMainMenu;
                }
            }

            // Default: allow exit
            return true;
        }

        private void RequestExitToMenUI()
        {
            if (_menUISystem != null)
            {
                if (enableDebugLogging)
                    Debug.Log("[LastUICanvasBridge] Requesting MenUI to close Last UI settings");

                _menUISystem.OnLastUISettingsComplete();
            }
            else
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUICanvasBridge] MenUI system not found, cannot request exit");
            }
        }

        /// <summary>
        /// Public method that can be called by UI buttons or other components
        /// </summary>
        public void ExitToMenUI()
        {
            if (enableDebugLogging)
                Debug.Log("[LastUICanvasBridge] ExitToMenUI called directly");

            RequestExitToMenUI();
        }

        /// <summary>
        /// Check if MenUI system is available
        /// </summary>
        public bool IsMenUISystemAvailable => _menUISystem != null;

        #endregion
    }
}
