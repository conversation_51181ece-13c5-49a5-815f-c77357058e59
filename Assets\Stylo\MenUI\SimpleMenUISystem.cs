using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using TMPro;
using Stylo.MenUI.UI;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple, streamlined pause menu system focused on core pause functionality.
    /// Settings are handled by Last UI canvas directly in the scene.
    /// Clean separation: MenUI = pause/resume, Last UI = comprehensive settings.
    /// </summary>
    public class SimpleMenUISystem : MonoBehaviour
    {
        [Header("UI Panels")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private GameObject overlayPanel;

        [Header("Pause Menu Buttons")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button exitButton;

        [Header("Configuration")]
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool pauseAudio = true;

        [Header("Last UI Integration")]
        [SerializeField] private GameObject lastUICanvas;
        [SerializeField] private bool hidePauseMenuWhenSettingsOpen = false;

        // Input system
        private DefaultControls _controls;
        private InputAction _pauseAction;

        // State
        private bool _isPaused = false;
        private float _previousTimeScale = 1f;

        // Components
        private StateManager _lastUIStateManager;
        private SceneServiceLocator _sceneServiceLocator;
        private LastUISettingsManager _lastUISettingsManager;

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeInput();
            SetupButtons();
            SetupExplicitNavigation();
            InitializeLastUIIntegration();
        }

        private void Start()
        {
            // Hide all panels initially
            HideAllPanels();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Initialized as simple pause menu. Settings handled by Last UI canvas.");
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                _pauseAction.performed += OnPauseInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                _pauseAction.performed -= OnPauseInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        #endregion

        #region Initialization

        private void InitializeInput()
        {
            _controls = new DefaultControls();
            _pauseAction = _controls.UI.Pause;
        }

        private void SetupButtons()
        {
            // Pause menu buttons
            if (resumeButton != null)
                resumeButton.onClick.AddListener(OnResumeClicked);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);

            if (exitButton != null)
                exitButton.onClick.AddListener(OnExitClicked);

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Core pause menu buttons setup complete");
        }

        private void HideAllPanels()
        {
            if (pauseMenuPanel != null) pauseMenuPanel.SetActive(false);
            if (overlayPanel != null) overlayPanel.SetActive(false);
        }

        #endregion

        #region Input Handling

        private void OnPauseInput(InputAction.CallbackContext context)
        {
            TogglePause();
        }

        #endregion

        #region Button Callbacks

        private void OnResumeClicked()
        {
            HidePause();
        }

        private void OnSettingsClicked()
        {
            if (enableDebugMode)
                Debug.Log($"SimpleMenUISystem: Settings button clicked. Current pause state: {_isPaused}, TimeScale: {Time.timeScale}");

            ShowLastUISettings();
        }

        private void OnExitClicked()
        {
            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Exit button clicked");

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }



        #endregion

        #region Public API

        public void TogglePause()
        {
            if (_isPaused)
                HidePause();
            else
                ShowPause();
        }

        public void ShowPause()
        {
            if (_isPaused) return;

            _isPaused = true;

            // Show overlay
            if (overlayPanel != null)
                overlayPanel.SetActive(true);

            // Show pause menu
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Pause systems
            PauseAllSystems();

            // Set focus
            if (resumeButton != null)
                resumeButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu shown");
        }

        public void HidePause()
        {
            if (!_isPaused) return;

            _isPaused = false;

            // Hide all panels
            HideAllPanels();

            // Resume systems
            ResumeAllSystems();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu hidden and game resumed");
        }

        /// <summary>
        /// Hide the pause menu visually without unpausing the game
        /// Used when loading settings to keep game paused but hide menu
        /// </summary>
        public void HidePauseMenuOnly()
        {
            // Hide all panels but keep pause state
            HideAllPanels();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu hidden (game remains paused)");
        }

        /// <summary>
        /// Show the pause menu visually without changing pause state
        /// Used when returning from settings to restore menu visibility
        /// </summary>
        public void ShowPauseMenuOnly()
        {
            // Show pause menu panels without changing pause state
            if (overlayPanel != null)
                overlayPanel.SetActive(true);

            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Set focus
            if (resumeButton != null)
                resumeButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu shown (pause state unchanged)");
        }

        /// <summary>
        /// Called by Last UI when settings are complete - hides Last UI and returns to pause menu
        /// </summary>
        public void OnLastUISettingsComplete()
        {
            HideLastUISettings();

            // Return to pause menu
            if (pauseMenuPanel != null)
            {
                pauseMenuPanel.SetActive(true);
                if (settingsButton != null)
                    settingsButton.Select();
            }

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Returned to pause menu from Last UI settings");
        }











        /// <summary>
        /// Ensures a selectable has proper navigation setup
        /// </summary>
        /// <param name="selectable">The selectable to setup</param>
        private void EnsureSelectableNavigation(Selectable selectable)
        {
            var nav = selectable.navigation;
            if (nav.mode == Navigation.Mode.None || nav.mode == Navigation.Mode.Automatic)
            {
                nav.mode = Navigation.Mode.Explicit;
                selectable.navigation = nav;
            }
        }

        /// <summary>
        /// Sets up vertical navigation chain for a list of selectables
        /// </summary>
        /// <param name="selectables">List of selectables to chain together</param>
        private void SetupVerticalNavigationChain(List<Selectable> selectables)
        {
            for (int i = 0; i < selectables.Count; i++)
            {
                var nav = selectables[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up/down navigation
                if (i > 0)
                    nav.selectOnUp = selectables[i - 1];

                if (i < selectables.Count - 1)
                    nav.selectOnDown = selectables[i + 1];

                selectables[i].navigation = nav;
            }

            if (enableDebugMode && selectables.Count > 0)
                Debug.Log($"SimpleMenUISystem: Setup navigation chain for {selectables.Count} selectables");
        }

        #endregion

        #region Controller Navigation Helpers





        /// <summary>
        /// Sets up explicit navigation between UI elements for better controller support
        /// </summary>
        private void SetupExplicitNavigation()
        {
            // Setup navigation for core pause menu buttons only
            SetupButtonNavigation(new[] { resumeButton, settingsButton, exitButton });

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Core pause menu navigation setup complete");
        }

        /// <summary>
        /// Sets up navigation between an array of buttons in vertical order
        /// </summary>
        /// <param name="buttons">Array of buttons to setup navigation for</param>
        private void SetupButtonNavigation(Button[] buttons)
        {
            if (buttons == null || buttons.Length == 0) return;

            for (int i = 0; i < buttons.Length; i++)
            {
                if (buttons[i] == null) continue;

                var nav = buttons[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up navigation
                if (i > 0 && buttons[i - 1] != null)
                    nav.selectOnUp = buttons[i - 1];

                if (i < buttons.Length - 1 && buttons[i + 1] != null)
                    nav.selectOnDown = buttons[i + 1];

                buttons[i].navigation = nav;
            }
        }





        #endregion

        #region System Control

        private void PauseAllSystems()
        {
            // Store current time scale and pause Unity
            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            // Pause audio
            if (pauseAudio)
                AudioListener.pause = true;

            // Pause Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.PauseTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems paused");
        }

        private void ResumeAllSystems()
        {
            // Restore time scale
            Time.timeScale = _previousTimeScale;

            // Resume audio
            if (pauseAudio)
                AudioListener.pause = false;

            // Resume Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.ResumeTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems resumed");
        }

        #endregion

        #region Last UI Integration

        /// <summary>
        /// Initialize the Last UI integration
        /// </summary>
        private void InitializeLastUIIntegration()
        {
            if (lastUICanvas != null)
            {
                // Find required components in the Last UI canvas
                _lastUIStateManager = lastUICanvas.GetComponentInChildren<StateManager>();
                _sceneServiceLocator = lastUICanvas.GetComponentInChildren<SceneServiceLocator>();
                _lastUISettingsManager = lastUICanvas.GetComponentInChildren<LastUISettingsManager>();

                // Create missing components if needed
                EnsureRequiredLastUIComponents();

                // Ensure Last UI canvas is initially hidden
                lastUICanvas.SetActive(false);

                if (enableDebugMode)
                    Debug.Log($"SimpleMenUISystem: Last UI integration initialized - StateManager: {(_lastUIStateManager != null)}, ServiceLocator: {(_sceneServiceLocator != null)}, SettingsManager: {(_lastUISettingsManager != null)}");
            }
            else
            {
                Debug.LogWarning("SimpleMenUISystem: No Last UI canvas assigned! Please assign the Last UI canvas in the inspector.");
            }
        }

        /// <summary>
        /// Ensure all required Last UI components are present
        /// </summary>
        private void EnsureRequiredLastUIComponents()
        {
            if (lastUICanvas == null) return;

            // Ensure SceneServiceLocator exists
            if (_sceneServiceLocator == null)
            {
                var locatorGO = new GameObject("SceneServiceLocator");
                locatorGO.transform.SetParent(lastUICanvas.transform);
                _sceneServiceLocator = locatorGO.AddComponent<SceneServiceLocator>();

                if (enableDebugMode)
                    Debug.Log("SimpleMenUISystem: Created SceneServiceLocator for Last UI");
            }

            // Ensure LastUISettingsManager exists
            if (_lastUISettingsManager == null)
            {
                var managerGO = new GameObject("LastUISettingsManager");
                managerGO.transform.SetParent(lastUICanvas.transform);
                _lastUISettingsManager = managerGO.AddComponent<LastUISettingsManager>();

                if (enableDebugMode)
                    Debug.Log("SimpleMenUISystem: Created LastUISettingsManager for Last UI");
            }

            // Warn if StateManager is missing (this should be manually added)
            if (_lastUIStateManager == null)
            {
                Debug.LogWarning("SimpleMenUISystem: No StateManager found in Last UI canvas. Settings navigation may not work properly. Please add a StateManager component to the Last UI canvas.");
            }
        }

        /// <summary>
        /// Show the Last UI settings canvas
        /// </summary>
        private void ShowLastUISettings()
        {
            if (lastUICanvas == null)
            {
                Debug.LogError("SimpleMenUISystem: Last UI canvas not assigned!");
                return;
            }

            // Hide pause menu if configured
            if (hidePauseMenuWhenSettingsOpen)
            {
                HidePauseMenuOnly();
                if (enableDebugMode)
                    Debug.Log("SimpleMenUISystem: Pause menu hidden while settings are open (game remains paused)");
            }

            // Show Last UI canvas - this will trigger OnEnable on all child components
            lastUICanvas.SetActive(true);

            // Since we simplified the structure and disabled StateManager, manually select first element
            StartCoroutine(SelectFirstSettingsElementDelayed());

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Last UI settings shown - manually selecting first element");
        }

        /// <summary>
        /// Select the first settings element after a short delay to ensure UI is ready
        /// </summary>
        private IEnumerator SelectFirstSettingsElementDelayed()
        {
            // Wait a frame for the UI to be fully activated
            yield return null;

            // Find the first selectable button in the active state_menu
            var stateMenu = lastUICanvas.transform.Find("state_menu");
            if (stateMenu != null && stateMenu.gameObject.activeInHierarchy)
            {
                var firstButton = stateMenu.GetComponentInChildren<UnityEngine.UI.Button>();
                if (firstButton != null)
                {
                    firstButton.Select();
                    if (enableDebugMode)
                        Debug.Log($"SimpleMenUISystem: Selected first button: {firstButton.name}");
                }
                else
                {
                    Debug.LogWarning("SimpleMenUISystem: No button found in state_menu for selection");
                }
            }
            else
            {
                Debug.LogWarning("SimpleMenUISystem: state_menu not found or not active");
            }
        }

        /// <summary>
        /// Hide the Last UI settings canvas
        /// </summary>
        private void HideLastUISettings()
        {
            if (lastUICanvas == null)
            {
                Debug.LogError("SimpleMenUISystem: Last UI canvas not assigned!");
                return;
            }

            // Hide Last UI canvas
            lastUICanvas.SetActive(false);

            // Show pause menu again if it was hidden
            if (hidePauseMenuWhenSettingsOpen)
            {
                ShowPauseMenuOnly();
                if (enableDebugMode)
                    Debug.Log("SimpleMenUISystem: Pause menu restored after settings closed (game remains paused)");
            }

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Last UI settings hidden");
        }



        /// <summary>
        /// Check if Last UI settings canvas is currently active
        /// </summary>
        public bool IsLastUIActive => lastUICanvas != null && lastUICanvas.activeInHierarchy;

        /// <summary>
        /// Get reference to the Last UI canvas
        /// </summary>
        public GameObject GetLastUICanvas() => lastUICanvas;

        /// <summary>
        /// Get reference to the Last UI StateManager
        /// </summary>
        public StateManager GetLastUIStateManager() => _lastUIStateManager;

        /// <summary>
        /// Get reference to the SceneServiceLocator
        /// </summary>
        public SceneServiceLocator GetSceneServiceLocator() => _sceneServiceLocator;

        /// <summary>
        /// Get reference to the LastUISettingsManager
        /// </summary>
        public LastUISettingsManager GetLastUISettingsManager() => _lastUISettingsManager;

        /// <summary>
        /// Check if the game is currently paused
        /// </summary>
        public bool IsPaused => _isPaused;

        /// <summary>
        /// Get current time scale for debugging
        /// </summary>
        public float CurrentTimeScale => Time.timeScale;

        #endregion
    }
}

// Force recompilation - Debug alias fix applied