using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using Debug = UnityEngine.Debug;

namespace Stylo.MenUI.UI
{
    /// <summary>
    /// Simple theme manager for MenUI that handles color schemes and basic styling.
    /// Simplified from complex theming architecture to focus on essential functionality.
    /// </summary>
    public class MenUIThemeManager : MonoBehaviour
    {
        [Header("Theme Settings")]
        [SerializeField] private ColorTheme currentTheme = ColorTheme.Dark;
        [SerializeField] private bool applyThemeOnStart = true;
        [SerializeField] private bool autoDetectUIElements = true;

        [Header("Debug")]
        [SerializeField] private bool enableDebugMode = false;

        // Available color themes
        public enum ColorTheme
        {
            Dark,
            Light,
            Blue,
            Green
        }

        // Theme color definitions
        [System.Serializable]
        public class ThemeColors
        {
            [Header("Background Colors")]
            public Color primaryBackground = new Color(0.1f, 0.1f, 0.1f, 0.9f);
            public Color secondaryBackground = new Color(0.2f, 0.2f, 0.2f, 0.9f);
            public Color overlayBackground = new Color(0f, 0f, 0f, 0.7f);

            [Header("Button Colors")]
            public Color buttonNormal = new Color(0.3f, 0.3f, 0.3f, 1f);
            public Color buttonHighlighted = new Color(0.4f, 0.4f, 0.4f, 1f);
            public Color buttonPressed = new Color(0.2f, 0.2f, 0.2f, 1f);
            public Color buttonSelected = new Color(0.5f, 0.5f, 0.5f, 1f);

            [Header("Text Colors")]
            public Color primaryText = Color.white;
            public Color secondaryText = new Color(0.8f, 0.8f, 0.8f, 1f);
            public Color disabledText = new Color(0.5f, 0.5f, 0.5f, 1f);

            [Header("Accent Colors")]
            public Color accentPrimary = new Color(0.2f, 0.6f, 1f, 1f);
            public Color accentSecondary = new Color(0.8f, 0.4f, 0.2f, 1f);
        }

        // Cached UI elements
        private List<Image> _backgroundImages = new List<Image>();
        private List<Button> _buttons = new List<Button>();
        private List<TextMeshProUGUI> _textElements = new List<TextMeshProUGUI>();

        private bool _isInitialized = false;

        private void Start()
        {
            if (autoDetectUIElements)
            {
                DetectUIElements();
            }

            if (applyThemeOnStart)
            {
                ApplyCurrentTheme();
            }

            _isInitialized = true;
        }

        /// <summary>
        /// Automatically detect UI elements to theme
        /// </summary>
        private void DetectUIElements()
        {
            _backgroundImages.Clear();
            _buttons.Clear();
            _textElements.Clear();

            // Find all Images (for backgrounds)
            Image[] allImages = GetComponentsInChildren<Image>();
            foreach (var image in allImages)
            {
                // Skip button images (they'll be handled separately)
                if (image.GetComponent<Button>() == null)
                {
                    _backgroundImages.Add(image);
                }
            }

            // Find all Buttons
            Button[] allButtons = GetComponentsInChildren<Button>();
            _buttons.AddRange(allButtons);

            // Find all TextMeshPro components
            TextMeshProUGUI[] allTexts = GetComponentsInChildren<TextMeshProUGUI>();
            _textElements.AddRange(allTexts);

            if (enableDebugMode)
            {
                Debug.Log($"[MenUIThemeManager] Detected {_backgroundImages.Count} backgrounds, {_buttons.Count} buttons, {_textElements.Count} text elements");
            }
        }

        /// <summary>
        /// Apply the current theme to all UI elements
        /// </summary>
        public void ApplyCurrentTheme()
        {
            ThemeColors colors = GetThemeColors(currentTheme);
            ApplyThemeColors(colors);

            if (enableDebugMode)
            {
                Debug.Log($"[MenUIThemeManager] Applied {currentTheme} theme");
            }
        }

        /// <summary>
        /// Get color scheme for the specified theme
        /// </summary>
        private ThemeColors GetThemeColors(ColorTheme theme)
        {
            return theme switch
            {
                ColorTheme.Dark => GetDarkTheme(),
                ColorTheme.Light => GetLightTheme(),
                ColorTheme.Blue => GetBlueTheme(),
                ColorTheme.Green => GetGreenTheme(),
                _ => GetDarkTheme()
            };
        }

        /// <summary>
        /// Apply theme colors to UI elements
        /// </summary>
        private void ApplyThemeColors(ThemeColors colors)
        {
            // Apply background colors
            foreach (var image in _backgroundImages)
            {
                if (image == null) continue;

                // Determine background type based on name or hierarchy
                if (image.name.ToLower().Contains("overlay"))
                {
                    image.color = colors.overlayBackground;
                }
                else if (image.name.ToLower().Contains("panel") || image.name.ToLower().Contains("background"))
                {
                    image.color = colors.primaryBackground;
                }
                else
                {
                    image.color = colors.secondaryBackground;
                }
            }

            // Apply button colors
            foreach (var button in _buttons)
            {
                if (button == null) continue;

                ColorBlock colorBlock = button.colors;
                colorBlock.normalColor = colors.buttonNormal;
                colorBlock.highlightedColor = colors.buttonHighlighted;
                colorBlock.pressedColor = colors.buttonPressed;
                colorBlock.selectedColor = colors.buttonSelected;
                colorBlock.disabledColor = colors.buttonNormal * 0.5f;
                button.colors = colorBlock;
            }

            // Apply text colors
            foreach (var text in _textElements)
            {
                if (text == null) continue;

                // Determine text type based on name or hierarchy
                if (text.name.ToLower().Contains("title") || text.name.ToLower().Contains("header"))
                {
                    text.color = colors.primaryText;
                }
                else if (text.name.ToLower().Contains("label") || text.name.ToLower().Contains("description"))
                {
                    text.color = colors.secondaryText;
                }
                else
                {
                    text.color = colors.primaryText;
                }
            }
        }

        #region Theme Definitions

        private ThemeColors GetDarkTheme()
        {
            return new ThemeColors
            {
                primaryBackground = new Color(0.1f, 0.1f, 0.1f, 0.95f),
                secondaryBackground = new Color(0.2f, 0.2f, 0.2f, 0.9f),
                overlayBackground = new Color(0f, 0f, 0f, 0.8f),
                buttonNormal = new Color(0.3f, 0.3f, 0.3f, 1f),
                buttonHighlighted = new Color(0.4f, 0.4f, 0.4f, 1f),
                buttonPressed = new Color(0.2f, 0.2f, 0.2f, 1f),
                buttonSelected = new Color(0.5f, 0.5f, 0.5f, 1f),
                primaryText = Color.white,
                secondaryText = new Color(0.8f, 0.8f, 0.8f, 1f),
                disabledText = new Color(0.5f, 0.5f, 0.5f, 1f),
                accentPrimary = new Color(0.2f, 0.6f, 1f, 1f),
                accentSecondary = new Color(0.8f, 0.4f, 0.2f, 1f)
            };
        }

        private ThemeColors GetLightTheme()
        {
            return new ThemeColors
            {
                primaryBackground = new Color(0.95f, 0.95f, 0.95f, 0.95f),
                secondaryBackground = new Color(0.85f, 0.85f, 0.85f, 0.9f),
                overlayBackground = new Color(0.3f, 0.3f, 0.3f, 0.8f),
                buttonNormal = new Color(0.7f, 0.7f, 0.7f, 1f),
                buttonHighlighted = new Color(0.8f, 0.8f, 0.8f, 1f),
                buttonPressed = new Color(0.6f, 0.6f, 0.6f, 1f),
                buttonSelected = new Color(0.9f, 0.9f, 0.9f, 1f),
                primaryText = new Color(0.1f, 0.1f, 0.1f, 1f),
                secondaryText = new Color(0.3f, 0.3f, 0.3f, 1f),
                disabledText = new Color(0.6f, 0.6f, 0.6f, 1f),
                accentPrimary = new Color(0.2f, 0.4f, 0.8f, 1f),
                accentSecondary = new Color(0.8f, 0.3f, 0.1f, 1f)
            };
        }

        private ThemeColors GetBlueTheme()
        {
            return new ThemeColors
            {
                primaryBackground = new Color(0.1f, 0.15f, 0.25f, 0.95f),
                secondaryBackground = new Color(0.15f, 0.2f, 0.3f, 0.9f),
                overlayBackground = new Color(0f, 0.05f, 0.15f, 0.8f),
                buttonNormal = new Color(0.2f, 0.3f, 0.5f, 1f),
                buttonHighlighted = new Color(0.3f, 0.4f, 0.6f, 1f),
                buttonPressed = new Color(0.1f, 0.2f, 0.4f, 1f),
                buttonSelected = new Color(0.4f, 0.5f, 0.7f, 1f),
                primaryText = Color.white,
                secondaryText = new Color(0.8f, 0.85f, 0.9f, 1f),
                disabledText = new Color(0.5f, 0.55f, 0.6f, 1f),
                accentPrimary = new Color(0.3f, 0.7f, 1f, 1f),
                accentSecondary = new Color(1f, 0.6f, 0.2f, 1f)
            };
        }

        private ThemeColors GetGreenTheme()
        {
            return new ThemeColors
            {
                primaryBackground = new Color(0.1f, 0.2f, 0.15f, 0.95f),
                secondaryBackground = new Color(0.15f, 0.25f, 0.2f, 0.9f),
                overlayBackground = new Color(0.05f, 0.1f, 0.05f, 0.8f),
                buttonNormal = new Color(0.2f, 0.4f, 0.3f, 1f),
                buttonHighlighted = new Color(0.3f, 0.5f, 0.4f, 1f),
                buttonPressed = new Color(0.1f, 0.3f, 0.2f, 1f),
                buttonSelected = new Color(0.4f, 0.6f, 0.5f, 1f),
                primaryText = Color.white,
                secondaryText = new Color(0.8f, 0.9f, 0.85f, 1f),
                disabledText = new Color(0.5f, 0.6f, 0.55f, 1f),
                accentPrimary = new Color(0.4f, 0.8f, 0.6f, 1f),
                accentSecondary = new Color(0.8f, 0.6f, 0.2f, 1f)
            };
        }

        #endregion

        #region Public API

        /// <summary>
        /// Set the current theme and apply it
        /// </summary>
        public void SetTheme(ColorTheme theme)
        {
            currentTheme = theme;
            if (_isInitialized)
            {
                ApplyCurrentTheme();
            }
        }

        /// <summary>
        /// Refresh UI element detection and apply current theme
        /// </summary>
        public void RefreshTheme()
        {
            if (autoDetectUIElements)
            {
                DetectUIElements();
            }
            ApplyCurrentTheme();
        }

        /// <summary>
        /// Get the current theme
        /// </summary>
        public ColorTheme GetCurrentTheme()
        {
            return currentTheme;
        }

        #endregion
    }
}
