# MenUI + Last UI Quick Setup Summary

## ✅ What's Already Done

The integration is **already implemented** and ready to use! Here's what's been set up:

### MenUI System

- ✅ `SimpleMenUISystem.cs` - Simple pause menu with Settings button
- ✅ `LastUISceneBridge.cs` - Handles async scene loading/unloading
- ✅ **Inspector Configuration** - Scene name configurable in SimpleMenUISystem
- ✅ **Auto-hide pause menu** when settings open (configurable)

### Last UI System

- ✅ **Domain reload fixes** - No more "StateManager not found" errors
- ✅ **Settings persistence** - All toggles/sliders save to ScriptableObject
- ✅ **Auto-configuration** - Toggle setting IDs assigned automatically
- ✅ **Exit handling** - Escape/Cancel returns to MenUI properly

## 🚀 How to Use

### 1. Setup Your Main Scene (30 seconds)

In your main game scene:

1. Add the `MenUI Canvas.prefab` from `Assets/Stylo/MenUI/Prefabs/`
2. Configure `SimpleMenUISystem` in inspector:
   - **Last UI Scene Name**: `"demo"` (or your custom scene name)
   - **Hide Pause Menu When Settings Open**: `true` (recommended)

### 2. Setup Last UI Scene (30 seconds)

In the Last UI scene (`demo.unity`):

1. Create empty GameObject named "DemoAutoSetup"
2. Add `DemoSceneAutoSetup` component
3. Done! All other components are created automatically

### 3. Test the Integration

1. **Enter Play Mode** in your main scene
2. **Press Escape** → MenUI pause menu appears
3. **Click "Settings"** → Last UI loads as overlay, pause menu stays visible, game remains paused
4. **Change some settings** → Toggle VSync, adjust volume, etc.
5. **Press Escape** → Last UI closes, returns to MenUI pause menu, game still paused
6. **Click "Settings" again** → All settings are preserved!

## 🎯 Key Features

### ✅ **Scene Configuration in Inspector**

```
SimpleMenUISystem:
├── Last UI Scene Name: "demo"
├── Hide Pause Menu When Settings Open: true
└── Enable Debug Mode: true
```

### ✅ **Automatic Setup**

- MenUI automatically adds `LastUISceneBridge` component
- Last UI automatically configures all required services
- Toggle setting IDs assigned automatically based on GameObject names

### ✅ **Smart Pause Management**

- Game remains paused when entering settings
- Pause menu stays visible as overlay (configurable)
- Escape key returns to MenUI from Last UI
- Controller Cancel button works

### ✅ **Settings Persistence**

- All settings save to ScriptableObject automatically
- Settings survive scene loads/unloads
- No data loss between MenUI ↔ Last UI transitions

## 🔧 Configuration Options

### SimpleMenUISystem Inspector

- **Last UI Scene Reference**: SceneReference asset pointing to settings scene
- **Hide Pause Menu When Settings Open**: `false` (keep pause menu visible as overlay)
- **Enable Debug Mode**: Show debug messages in console

### Advanced Configuration

- **Exit conditions**: Modify `LastUIMenUIBridge.ShouldExitToMenUI()`
- **Input handling**: Configure escape/cancel behavior
- **Scene loading**: Customize loading/unloading behavior

## 🎉 Benefits

✅ **Simple Integration** - Just add prefab and one component  
✅ **Modular Design** - MenUI and Last UI remain independent  
✅ **Performance** - Settings only loaded when needed  
✅ **User-Friendly** - Seamless navigation between pause and settings  
✅ **Persistent** - Settings survive all transitions  
✅ **Configurable** - Easy to customize via inspector

## 🐛 Troubleshooting

### Scene Loading Issues

- Ensure Last UI scene is in Build Settings
- Check scene name spelling in SimpleMenUISystem inspector

### Settings Not Working

- Verify `DemoSceneAutoSetup` component exists in Last UI scene
- Check console for auto-configuration messages

### Navigation Issues

- Ensure both scenes have proper input system setup
- Check for "StateManager not found" errors (should be fixed)

The integration is **production-ready** and provides a clean separation between pause functionality and comprehensive settings!
