using UnityEngine;

/// <summary>
/// Automatically sets up the demo scene with required components.
/// This ensures the demo scene works out of the box.
/// </summary>
[DefaultExecutionOrder(-200)] // Execute very early
public class DemoSceneAutoSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    [SerializeField] private bool enableDebugLogging = true;

    private void Awake()
    {
        if (enableDebugLogging)
            Debug.Log("[DemoSceneAutoSetup] Setting up demo scene");

        SetupRequiredComponents();

        if (enableDebugLogging)
            Debug.Log("[DemoSceneAutoSetup] Demo scene setup complete");
    }

    private void SetupRequiredComponents()
    {
        // Ensure SceneServiceLocator exists
        var locator = FindObjectOfType<SceneServiceLocator>();
        if (locator == null)
        {
            var locatorGO = new GameObject("SceneServiceLocator");
            locator = locatorGO.AddComponent<SceneServiceLocator>();
            if (enableDebugLogging)
                Debug.Log("[DemoSceneAutoSetup] Created SceneServiceLocator");
        }

        // Ensure LastUISettingsManager exists
        var settingsManager = FindObjectOfType<LastUISettingsManager>();
        if (settingsManager == null)
        {
            var managerGO = new GameObject("LastUISettingsManager");
            settingsManager = managerGO.AddComponent<LastUISettingsManager>();
            if (enableDebugLogging)
                Debug.Log("[DemoSceneAutoSetup] Created LastUISettingsManager");
        }

        // Ensure LastUISceneSetup exists
        var sceneSetup = FindObjectOfType<LastUISceneSetup>();
        if (sceneSetup == null)
        {
            var setupGO = new GameObject("LastUISceneSetup");
            sceneSetup = setupGO.AddComponent<LastUISceneSetup>();
            if (enableDebugLogging)
                Debug.Log("[DemoSceneAutoSetup] Created LastUISceneSetup");
        }

        // Ensure SwitchHandlerAutoConfig exists and configure toggles
        var switchConfig = FindObjectOfType<SwitchHandlerAutoConfig>();
        if (switchConfig == null)
        {
            var configGO = new GameObject("SwitchHandlerAutoConfig");
            switchConfig = configGO.AddComponent<SwitchHandlerAutoConfig>();
            if (enableDebugLogging)
                Debug.Log("[DemoSceneAutoSetup] Created SwitchHandlerAutoConfig");
        }
        else
        {
            // If it already exists, trigger configuration
            switchConfig.ConfigureAllSwitchHandlers();
        }

        // Note: LastUIInputBridge is now created automatically by LastUISceneBridge
        // when the Last UI scene is loaded, so we don't need to create it here.
        // The bridge will be properly initialized with the scene bridge reference.
        if (enableDebugLogging)
            Debug.Log("[DemoSceneAutoSetup] LastUIInputBridge will be created automatically by LastUISceneBridge");
    }
}
