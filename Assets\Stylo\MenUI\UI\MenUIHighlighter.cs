using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections;

namespace Stylo.MenUI.UI
{
    /// <summary>
    /// Provides visual highlighting for UI elements when selected via controller navigation.
    /// Automatically detects when UI elements are selected and applies visual feedback.
    /// </summary>
    public class MenUIHighlighter : MonoBehaviour
    {
        [Header("Highlight Settings")]
        [SerializeField] private Color highlightColor = new Color(1f, 1f, 1f, 0.3f);
        [SerializeField] private Color highlightBorderColor = new Color(0.2f, 0.6f, 1f, 1f);
        [SerializeField] private float highlightBorderWidth = 3f;
        [SerializeField] private float animationSpeed = 5f;
        [SerializeField] private bool enablePulseEffect = true;
        [SerializeField] private float pulseSpeed = 2f;
        [SerializeField] private float pulseIntensity = 0.2f;

        [Header("Audio Feedback")]
        [SerializeField] private bool enableAudioFeedback = true;
        [SerializeField] private AudioClip navigationSound;
        [SerializeField] private AudioClip selectionSound;
        [SerializeField] private float audioVolume = 0.5f;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogging = false;

        private GameObject _currentHighlightedObject;
        private GameObject _highlightOverlay;
        private Image _highlightImage;
        private Outline _highlightOutline;
        private AudioSource _audioSource;
        private Coroutine _pulseCoroutine;
        private EventSystem _eventSystem;

        private void Start()
        {
            _eventSystem = EventSystem.current;
            CreateHighlightOverlay();
            SetupAudioSource();
        }

        private void Update()
        {
            CheckForSelectionChange();
        }

        /// <summary>
        /// Create the highlight overlay that will be positioned over selected UI elements
        /// </summary>
        private void CreateHighlightOverlay()
        {
            // Create highlight overlay GameObject
            _highlightOverlay = new GameObject("MenUI Highlight Overlay");
            _highlightOverlay.transform.SetParent(transform);

            // Add RectTransform
            var rectTransform = _highlightOverlay.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // Add Image component for background highlight
            _highlightImage = _highlightOverlay.AddComponent<Image>();
            _highlightImage.color = highlightColor;
            _highlightImage.raycastTarget = false;

            // Add Outline component for border highlight
            _highlightOutline = _highlightOverlay.AddComponent<Outline>();
            _highlightOutline.effectColor = highlightBorderColor;
            _highlightOutline.effectDistance = new Vector2(highlightBorderWidth, highlightBorderWidth);
            _highlightOutline.useGraphicAlpha = false;

            // Start hidden
            _highlightOverlay.SetActive(false);

            if (enableDebugLogging)
                Debug.Log("[MenUIHighlighter] Highlight overlay created");
        }

        /// <summary>
        /// Setup audio source for navigation feedback
        /// </summary>
        private void SetupAudioSource()
        {
            if (!enableAudioFeedback) return;

            _audioSource = gameObject.AddComponent<AudioSource>();
            _audioSource.playOnAwake = false;
            _audioSource.volume = audioVolume;
            _audioSource.spatialBlend = 0f; // 2D sound
        }

        /// <summary>
        /// Check if the selected UI element has changed
        /// </summary>
        private void CheckForSelectionChange()
        {
            if (_eventSystem == null) return;

            var currentSelected = _eventSystem.currentSelectedGameObject;

            if (currentSelected != _currentHighlightedObject)
            {
                if (_currentHighlightedObject != null)
                {
                    OnElementDeselected(_currentHighlightedObject);
                }

                _currentHighlightedObject = currentSelected;

                if (_currentHighlightedObject != null)
                {
                    OnElementSelected(_currentHighlightedObject);
                }
                else
                {
                    HideHighlight();
                }
            }
        }

        /// <summary>
        /// Called when a UI element is selected
        /// </summary>
        /// <param name="selectedObject">The selected UI element</param>
        private void OnElementSelected(GameObject selectedObject)
        {
            if (IsValidUIElement(selectedObject))
            {
                ShowHighlight(selectedObject);
                PlayNavigationSound();

                if (enableDebugLogging)
                    Debug.Log($"[MenUIHighlighter] Element selected: {selectedObject.name}");
            }
        }

        /// <summary>
        /// Called when a UI element is deselected
        /// </summary>
        /// <param name="deselectedObject">The deselected UI element</param>
        private void OnElementDeselected(GameObject deselectedObject)
        {
            if (enableDebugLogging)
                Debug.Log($"[MenUIHighlighter] Element deselected: {deselectedObject.name}");
        }

        /// <summary>
        /// Check if the GameObject is a valid UI element that should be highlighted
        /// </summary>
        /// <param name="obj">The GameObject to check</param>
        /// <returns>True if it should be highlighted</returns>
        private bool IsValidUIElement(GameObject obj)
        {
            if (obj == null) return false;

            // Check if it has a Selectable component (Button, Toggle, Dropdown, Slider, etc.)
            var selectable = obj.GetComponent<Selectable>();
            if (selectable == null) return false;

            // Check if it's interactable
            if (!selectable.interactable) return false;

            // Check if it's active in hierarchy
            if (!obj.activeInHierarchy) return false;

            return true;
        }

        /// <summary>
        /// Show highlight overlay on the specified UI element
        /// </summary>
        /// <param name="targetElement">The UI element to highlight</param>
        private void ShowHighlight(GameObject targetElement)
        {
            if (_highlightOverlay == null || targetElement == null) return;

            var targetRect = targetElement.GetComponent<RectTransform>();
            if (targetRect == null) return;

            // Position the highlight overlay
            var highlightRect = _highlightOverlay.GetComponent<RectTransform>();

            // Set the highlight to match the target element's position and size
            highlightRect.position = targetRect.position;
            highlightRect.sizeDelta = targetRect.sizeDelta;

            // Ensure highlight is behind the target element but visible
            _highlightOverlay.transform.SetSiblingIndex(targetElement.transform.GetSiblingIndex());

            // Show the highlight
            _highlightOverlay.SetActive(true);

            // Start pulse effect if enabled
            if (enablePulseEffect)
            {
                if (_pulseCoroutine != null)
                    StopCoroutine(_pulseCoroutine);
                _pulseCoroutine = StartCoroutine(PulseEffect());
            }

            // Animate the highlight appearance
            StartCoroutine(AnimateHighlightIn());
        }

        /// <summary>
        /// Hide the highlight overlay
        /// </summary>
        private void HideHighlight()
        {
            if (_highlightOverlay == null) return;

            // Stop pulse effect
            if (_pulseCoroutine != null)
            {
                StopCoroutine(_pulseCoroutine);
                _pulseCoroutine = null;
            }

            // Animate out and hide
            StartCoroutine(AnimateHighlightOut());
        }

        /// <summary>
        /// Animate the highlight appearing
        /// </summary>
        private IEnumerator AnimateHighlightIn()
        {
            if (_highlightImage == null) yield break;

            float startAlpha = 0f;
            float targetAlpha = highlightColor.a;
            float elapsed = 0f;
            float duration = 1f / animationSpeed;

            Color startColor = highlightColor;
            startColor.a = startAlpha;
            _highlightImage.color = startColor;

            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(startAlpha, targetAlpha, elapsed / duration);

                Color currentColor = highlightColor;
                currentColor.a = alpha;
                _highlightImage.color = currentColor;

                yield return null;
            }

            Color finalColor = highlightColor;
            finalColor.a = targetAlpha;
            _highlightImage.color = finalColor;
        }

        /// <summary>
        /// Animate the highlight disappearing
        /// </summary>
        private IEnumerator AnimateHighlightOut()
        {
            if (_highlightImage == null) yield break;

            float startAlpha = _highlightImage.color.a;
            float targetAlpha = 0f;
            float elapsed = 0f;
            float duration = 1f / animationSpeed;

            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(startAlpha, targetAlpha, elapsed / duration);

                Color currentColor = highlightColor;
                currentColor.a = alpha;
                _highlightImage.color = currentColor;

                yield return null;
            }

            _highlightOverlay.SetActive(false);
        }

        /// <summary>
        /// Create a pulsing effect for the highlight
        /// </summary>
        private IEnumerator PulseEffect()
        {
            if (_highlightImage == null) yield break;

            float baseAlpha = highlightColor.a;

            while (true)
            {
                float pulse = Mathf.Sin(Time.unscaledTime * pulseSpeed) * pulseIntensity;
                float alpha = Mathf.Clamp01(baseAlpha + pulse);

                Color currentColor = highlightColor;
                currentColor.a = alpha;
                _highlightImage.color = currentColor;

                yield return null;
            }
        }

        /// <summary>
        /// Play navigation sound effect
        /// </summary>
        private void PlayNavigationSound()
        {
            if (!enableAudioFeedback || _audioSource == null || navigationSound == null) return;

            _audioSource.clip = navigationSound;
            _audioSource.Play();
        }

        /// <summary>
        /// Play selection sound effect
        /// </summary>
        public void PlaySelectionSound()
        {
            if (!enableAudioFeedback || _audioSource == null || selectionSound == null) return;

            _audioSource.clip = selectionSound;
            _audioSource.Play();
        }

        /// <summary>
        /// Manually highlight a specific UI element
        /// </summary>
        /// <param name="element">The UI element to highlight</param>
        public void HighlightElement(GameObject element)
        {
            if (element != null && IsValidUIElement(element))
            {
                element.GetComponent<Selectable>()?.Select();
            }
        }

        /// <summary>
        /// Set highlight colors
        /// </summary>
        /// <param name="backgroundColor">Background highlight color</param>
        /// <param name="borderColor">Border highlight color</param>
        public void SetHighlightColors(Color backgroundColor, Color borderColor)
        {
            highlightColor = backgroundColor;
            highlightBorderColor = borderColor;

            if (_highlightImage != null)
                _highlightImage.color = highlightColor;

            if (_highlightOutline != null)
                _highlightOutline.effectColor = highlightBorderColor;
        }

        private void OnDestroy()
        {
            if (_pulseCoroutine != null)
                StopCoroutine(_pulseCoroutine);
        }
    }
}
