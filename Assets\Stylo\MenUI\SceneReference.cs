using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Stylo.MenUI
{
    /// <summary>
    /// ScriptableObject-based scene reference system.
    /// Provides a robust way to reference scenes with validation and build settings integration.
    /// </summary>
    [CreateAssetMenu(fileName = "New Scene Reference", menuName = "Stylo/MenUI/Scene Reference", order = 1)]
    public class SceneReference : ScriptableObject
    {
        [Header("Scene Configuration")]
        [SerializeField] private SceneAsset sceneAsset;
        [SerializeField] private string sceneName;
        [SerializeField] private bool autoUpdateName = true;

        [Header("Validation")]
        [SerializeField] private bool isInBuildSettings;
        [SerializeField] private int buildIndex = -1;

        /// <summary>
        /// Get the scene name for loading
        /// </summary>
        public string SceneName
        {
            get
            {
#if UNITY_EDITOR
                if (autoUpdateName && sceneAsset != null)
                {
                    UpdateSceneInfo();
                }
#endif
                return sceneName;
            }
        }

        /// <summary>
        /// Check if the scene is valid and can be loaded
        /// </summary>
        public bool IsValid => !string.IsNullOrEmpty(sceneName) && isInBuildSettings;

        /// <summary>
        /// Get the build index of the scene
        /// </summary>
        public int BuildIndex => buildIndex;

        /// <summary>
        /// Check if scene is in build settings
        /// </summary>
        public bool IsInBuildSettings => isInBuildSettings;

        /// <summary>
        /// Get the scene asset (editor only)
        /// </summary>
        public SceneAsset SceneAsset => sceneAsset;

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (autoUpdateName)
            {
                UpdateSceneInfo();
            }
        }

        /// <summary>
        /// Update scene information from the assigned SceneAsset
        /// </summary>
        private void UpdateSceneInfo()
        {
            if (sceneAsset == null)
            {
                sceneName = "";
                isInBuildSettings = false;
                buildIndex = -1;
                return;
            }

            // Get scene name from asset
            string assetPath = AssetDatabase.GetAssetPath(sceneAsset);
            sceneName = System.IO.Path.GetFileNameWithoutExtension(assetPath);

            // Check build settings
            var buildScenes = EditorBuildSettings.scenes;
            isInBuildSettings = false;
            buildIndex = -1;

            for (int i = 0; i < buildScenes.Length; i++)
            {
                if (buildScenes[i].path == assetPath && buildScenes[i].enabled)
                {
                    isInBuildSettings = true;
                    buildIndex = i;
                    break;
                }
            }

            // Mark as dirty to save changes
            EditorUtility.SetDirty(this);
        }

        /// <summary>
        /// Add this scene to build settings if not already present
        /// </summary>
        [ContextMenu("Add to Build Settings")]
        public void AddToBuildSettings()
        {
            if (sceneAsset == null)
            {
                Debug.LogWarning("[SceneReference] No scene asset assigned");
                return;
            }

            string assetPath = AssetDatabase.GetAssetPath(sceneAsset);
            var buildScenes = EditorBuildSettings.scenes;

            // Check if already in build settings
            foreach (var scene in buildScenes)
            {
                if (scene.path == assetPath)
                {
                    Debug.Log($"[SceneReference] Scene '{sceneName}' is already in build settings");
                    return;
                }
            }

            // Add to build settings
            var newBuildScenes = new EditorBuildSettingsScene[buildScenes.Length + 1];
            System.Array.Copy(buildScenes, newBuildScenes, buildScenes.Length);
            newBuildScenes[buildScenes.Length] = new EditorBuildSettingsScene(assetPath, true);

            EditorBuildSettings.scenes = newBuildScenes;
            UpdateSceneInfo();

            Debug.Log($"[SceneReference] Added scene '{sceneName}' to build settings at index {buildIndex}");
        }

        /// <summary>
        /// Validate the scene reference and log any issues
        /// </summary>
        [ContextMenu("Validate Scene Reference")]
        public void ValidateReference()
        {
            UpdateSceneInfo();

            Debug.Log($"[SceneReference] Validation for '{name}':");
            Debug.Log($"  - Scene Asset: {(sceneAsset != null ? sceneAsset.name : "None")}");
            Debug.Log($"  - Scene Name: {sceneName}");
            Debug.Log($"  - In Build Settings: {isInBuildSettings}");
            Debug.Log($"  - Build Index: {buildIndex}");
            Debug.Log($"  - Is Valid: {IsValid}");

            if (!IsValid)
            {
                if (sceneAsset == null)
                {
                    Debug.LogWarning("[SceneReference] No scene asset assigned!");
                }
                else if (!isInBuildSettings)
                {
                    Debug.LogWarning($"[SceneReference] Scene '{sceneName}' is not in build settings. Use 'Add to Build Settings' context menu.");
                }
            }
        }
#endif

        /// <summary>
        /// Get a user-friendly description of this scene reference
        /// </summary>
        public override string ToString()
        {
            if (string.IsNullOrEmpty(sceneName))
                return "No Scene Assigned";

            return $"{sceneName} {(IsValid ? "(Valid)" : "(Invalid)")}";
        }
    }
}
