using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using Debug = UnityEngine.Debug;

namespace Stylo.MenUI.UI
{
    /// <summary>
    /// Dynamic scaling system for MenUI that allows real-time adjustment of all UI proportions.
    /// Attach to the main Canvas GameObject to control all MenUI elements.
    /// Simplified from complex Core architecture to focus on essential functionality.
    /// </summary>
    public class MenUIScaler : MonoBehaviour
    {
        [Header("UI Scale Presets")]
        [SerializeField] private UIScalePreset currentScale = UIScalePreset.Standard;

        [Header("Custom Scale Override")]
        [SerializeField] private bool useCustomScale = false;
        [SerializeField, Range(0.5f, 2.0f)] private float customScaleMultiplier = 1.0f;

        [Header("Real-time Adjustment")]
        [SerializeField] private bool enableRuntimeScaling = true;
        [SerializeField] private bool autoDetectUIElements = true;

        [Header("Debug")]
        [SerializeField] private bool enableDebugMode = false;

        // UI Scale Presets
        public enum UIScalePreset
        {
            Small = 0,      // 0.8x scale
            Standard = 1,   // 1.0x scale
            Large = 2,      // 1.2x scale
            ExtraLarge = 3  // 1.5x scale
        }

        // Scale configuration data
        [System.Serializable]
        public class ScaleConfig
        {
            [Header("Layout Scaling")]
            public float paddingMultiplier = 1.0f;
            public float spacingMultiplier = 1.0f;
            public float buttonWidthMultiplier = 1.0f;
            public float buttonHeightMultiplier = 1.0f;

            [Header("Text Scaling")]
            public float fontSizeMultiplier = 1.0f;
        }

        // Cached components
        private Canvas _targetCanvas;
        private CanvasScaler _canvasScaler;
        private List<RectTransform> _uiElements = new List<RectTransform>();
        private List<TextMeshProUGUI> _textElements = new List<TextMeshProUGUI>();

        // State tracking
        private UIScalePreset _lastScale;
        private float _lastCustomScale;
        private bool _isInitialized = false;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (autoDetectUIElements)
            {
                DetectUIElements();
            }

            ApplyScaling();
            _isInitialized = true;
        }

        private void Update()
        {
            if (!enableRuntimeScaling || !_isInitialized) return;

            // Check if scale settings have changed
            bool scaleChanged = false;

            if (useCustomScale && _lastCustomScale != customScaleMultiplier)
            {
                scaleChanged = true;
                _lastCustomScale = customScaleMultiplier;
            }
            else if (!useCustomScale && _lastScale != currentScale)
            {
                scaleChanged = true;
                _lastScale = currentScale;
            }

            if (scaleChanged)
            {
                ApplyScaling();
            }
        }

        /// <summary>
        /// Initialize canvas and scaler components
        /// </summary>
        private void InitializeComponents()
        {
            _targetCanvas = GetComponent<Canvas>();
            if (_targetCanvas == null)
            {
                _targetCanvas = GetComponentInParent<Canvas>();
            }

            if (_targetCanvas != null)
            {
                _canvasScaler = _targetCanvas.GetComponent<CanvasScaler>();
            }

            if (enableDebugMode)
            {
                Debug.Log($"[MenUIScaler] Initialized with Canvas: {(_targetCanvas != null ? "Found" : "Not Found")}");
            }
        }

        /// <summary>
        /// Automatically detect UI elements to scale
        /// </summary>
        private void DetectUIElements()
        {
            _uiElements.Clear();
            _textElements.Clear();

            if (_targetCanvas == null) return;

            // Find all RectTransforms
            RectTransform[] allRects = _targetCanvas.GetComponentsInChildren<RectTransform>();
            foreach (var rect in allRects)
            {
                if (rect != _targetCanvas.transform)
                {
                    _uiElements.Add(rect);
                }
            }

            // Find all TextMeshPro components
            TextMeshProUGUI[] allTexts = _targetCanvas.GetComponentsInChildren<TextMeshProUGUI>();
            _textElements.AddRange(allTexts);

            if (enableDebugMode)
            {
                Debug.Log($"[MenUIScaler] Detected {_uiElements.Count} UI elements and {_textElements.Count} text elements");
            }
        }

        /// <summary>
        /// Apply current scaling settings
        /// </summary>
        private void ApplyScaling()
        {
            float scaleFactor = GetCurrentScaleFactor();
            ScaleConfig config = GetScaleConfig(scaleFactor);

            // Apply canvas scaling if available
            if (_canvasScaler != null)
            {
                _canvasScaler.scaleFactor = scaleFactor;
            }

            // Apply element-specific scaling
            ApplyElementScaling(config);

            if (enableDebugMode)
            {
                Debug.Log($"[MenUIScaler] Applied scaling with factor: {scaleFactor:F2}");
            }
        }

        /// <summary>
        /// Get the current scale factor based on settings
        /// </summary>
        private float GetCurrentScaleFactor()
        {
            if (useCustomScale)
            {
                return customScaleMultiplier;
            }

            return currentScale switch
            {
                UIScalePreset.Small => 0.8f,
                UIScalePreset.Standard => 1.0f,
                UIScalePreset.Large => 1.2f,
                UIScalePreset.ExtraLarge => 1.5f,
                _ => 1.0f
            };
        }

        /// <summary>
        /// Get scale configuration for the given factor
        /// </summary>
        private ScaleConfig GetScaleConfig(float scaleFactor)
        {
            return new ScaleConfig
            {
                paddingMultiplier = scaleFactor,
                spacingMultiplier = scaleFactor,
                buttonWidthMultiplier = scaleFactor,
                buttonHeightMultiplier = scaleFactor,
                fontSizeMultiplier = scaleFactor
            };
        }

        /// <summary>
        /// Apply scaling to individual UI elements
        /// </summary>
        private void ApplyElementScaling(ScaleConfig config)
        {
            // Scale layout groups
            foreach (var element in _uiElements)
            {
                if (element == null) continue;

                // Scale layout groups
                var layoutGroup = element.GetComponent<LayoutGroup>();
                if (layoutGroup != null)
                {
                    if (layoutGroup is HorizontalLayoutGroup hlg)
                    {
                        hlg.spacing *= config.spacingMultiplier;
                        hlg.padding.left = Mathf.RoundToInt(hlg.padding.left * config.paddingMultiplier);
                        hlg.padding.right = Mathf.RoundToInt(hlg.padding.right * config.paddingMultiplier);
                        hlg.padding.top = Mathf.RoundToInt(hlg.padding.top * config.paddingMultiplier);
                        hlg.padding.bottom = Mathf.RoundToInt(hlg.padding.bottom * config.paddingMultiplier);
                    }
                    else if (layoutGroup is VerticalLayoutGroup vlg)
                    {
                        vlg.spacing *= config.spacingMultiplier;
                        vlg.padding.left = Mathf.RoundToInt(vlg.padding.left * config.paddingMultiplier);
                        vlg.padding.right = Mathf.RoundToInt(vlg.padding.right * config.paddingMultiplier);
                        vlg.padding.top = Mathf.RoundToInt(vlg.padding.top * config.paddingMultiplier);
                        vlg.padding.bottom = Mathf.RoundToInt(vlg.padding.bottom * config.paddingMultiplier);
                    }
                }

                // Scale layout elements
                var layoutElement = element.GetComponent<LayoutElement>();
                if (layoutElement != null)
                {
                    if (layoutElement.minWidth > 0)
                        layoutElement.minWidth *= config.buttonWidthMultiplier;
                    if (layoutElement.preferredWidth > 0)
                        layoutElement.preferredWidth *= config.buttonWidthMultiplier;
                    if (layoutElement.minHeight > 0)
                        layoutElement.minHeight *= config.buttonHeightMultiplier;
                    if (layoutElement.preferredHeight > 0)
                        layoutElement.preferredHeight *= config.buttonHeightMultiplier;
                }
            }

            // Scale text elements
            foreach (var textElement in _textElements)
            {
                if (textElement != null)
                {
                    textElement.fontSize *= config.fontSizeMultiplier;
                }
            }
        }

        #region Public API

        /// <summary>
        /// Set the UI scale preset and immediately apply it
        /// </summary>
        public void SetScalePreset(UIScalePreset preset)
        {
            currentScale = preset;
            useCustomScale = false;

            if (_isInitialized)
            {
                ApplyScaling();
                _lastScale = currentScale;
            }
        }

        /// <summary>
        /// Set a custom scale multiplier and immediately apply it
        /// </summary>
        public void SetCustomScale(float multiplier)
        {
            customScaleMultiplier = Mathf.Clamp(multiplier, 0.5f, 2.0f);
            useCustomScale = true;

            if (_isInitialized)
            {
                ApplyScaling();
                _lastCustomScale = customScaleMultiplier;
            }
        }

        /// <summary>
        /// Reset to standard scaling
        /// </summary>
        public void ResetToStandard()
        {
            SetScalePreset(UIScalePreset.Standard);
        }

        /// <summary>
        /// Refresh UI element detection and apply scaling
        /// </summary>
        public void RefreshScaling()
        {
            if (autoDetectUIElements)
            {
                DetectUIElements();
            }
            ApplyScaling();
        }

        /// <summary>
        /// Get current scale factor
        /// </summary>
        public float GetScaleFactor()
        {
            return GetCurrentScaleFactor();
        }

        #endregion
    }
}
