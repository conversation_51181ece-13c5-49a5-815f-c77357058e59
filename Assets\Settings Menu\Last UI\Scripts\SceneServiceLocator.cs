using UnityEngine;
using System.Collections.Generic;
using System;

/// <summary>
/// Scene-scoped service locator that eliminates domain reload issues.
/// Each scene manages its own services independently without global singletons.
/// </summary>
public class SceneServiceLocator : MonoBehaviour
{
    private static Dictionary<int, SceneServiceLocator> _sceneLocators = new Dictionary<int, SceneServiceLocator>();
    private Dictionary<Type, Component> _services = new Dictionary<Type, Component>();

    [Header("Configuration")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoRegisterComponents = true;

    // Domain reload handling
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
    private static void ResetStaticData()
    {
        _sceneLocators.Clear();
        Debug.Log("[SceneServiceLocator] Static data reset for domain reload");
    }

    private void Awake()
    {
        int sceneInstanceID = gameObject.scene.GetHashCode();

        if (_sceneLocators.ContainsKey(sceneInstanceID))
        {
            if (enableDebugLogging)
                Debug.LogWarning($"[SceneServiceLocator] Multiple service locators in scene {gameObject.scene.name}. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }

        _sceneLocators[sceneInstanceID] = this;

        if (enableDebugLogging)
            Debug.Log($"[SceneServiceLocator] Initialized for scene: {gameObject.scene.name}");

        if (autoRegisterComponents)
        {
            AutoRegisterServices();
        }
    }

    private void OnDestroy()
    {
        int sceneInstanceID = gameObject.scene.GetHashCode();
        if (_sceneLocators.ContainsKey(sceneInstanceID) && _sceneLocators[sceneInstanceID] == this)
        {
            _sceneLocators.Remove(sceneInstanceID);
            if (enableDebugLogging)
                Debug.Log($"[SceneServiceLocator] Cleaned up for scene: {gameObject.scene.name}");
        }
    }

    /// <summary>
    /// Register a service in the current scene
    /// </summary>
    public static void Register<T>(T service) where T : Component
    {
        var locator = GetLocatorForScene(service.gameObject.scene);
        if (locator != null)
        {
            locator.RegisterService(service);
        }
    }

    /// <summary>
    /// Get a service from the current scene
    /// </summary>
    public static T Get<T>() where T : Component
    {
        // Try to find service in the active scene first
        var activeScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
        var locator = GetLocatorForScene(activeScene);

        if (locator != null)
        {
            var service = locator.GetService<T>();
            if (service != null)
                return service;
        }

        // If not found in active scene, search all scenes
        foreach (var kvp in _sceneLocators)
        {
            var service = kvp.Value.GetService<T>();
            if (service != null)
                return service;
        }

        Debug.LogWarning($"[SceneServiceLocator] Service of type {typeof(T).Name} not found in any scene");
        return null;
    }

    /// <summary>
    /// Get a service from a specific scene
    /// </summary>
    public static T GetFromScene<T>(UnityEngine.SceneManagement.Scene scene) where T : Component
    {
        var locator = GetLocatorForScene(scene);
        return locator?.GetService<T>();
    }

    /// <summary>
    /// Check if a service is registered
    /// </summary>
    public static bool IsRegistered<T>() where T : Component
    {
        return Get<T>() != null;
    }

    private static SceneServiceLocator GetLocatorForScene(UnityEngine.SceneManagement.Scene scene)
    {
        int sceneInstanceID = scene.GetHashCode();
        _sceneLocators.TryGetValue(sceneInstanceID, out var locator);
        return locator;
    }

    private void RegisterService<T>(T service) where T : Component
    {
        Type serviceType = typeof(T);

        if (_services.ContainsKey(serviceType))
        {
            if (enableDebugLogging)
                Debug.LogWarning($"[SceneServiceLocator] Service {serviceType.Name} already registered. Replacing with new instance.");
        }

        _services[serviceType] = service;

        if (enableDebugLogging)
            Debug.Log($"[SceneServiceLocator] Registered service: {serviceType.Name}");
    }

    private T GetService<T>() where T : Component
    {
        Type serviceType = typeof(T);

        if (_services.TryGetValue(serviceType, out var service))
        {
            return service as T;
        }

        return null;
    }

    /// <summary>
    /// Automatically register common services found in the scene
    /// </summary>
    private void AutoRegisterServices()
    {
        // Find and register StateManager
        var stateManager = FindObjectOfType<StateManager>();
        if (stateManager != null)
        {
            RegisterService(stateManager);
        }

        // Find and register LastUISettingsManager
        var settingsManager = FindObjectOfType<LastUISettingsManager>();
        if (settingsManager != null)
        {
            RegisterService(settingsManager);
        }

        // Find and register UIInputManager
        var inputManager = FindObjectOfType<UIInputManager>();
        if (inputManager != null)
        {
            RegisterService(inputManager);
        }

        if (enableDebugLogging)
            Debug.Log($"[SceneServiceLocator] Auto-registered {_services.Count} services");
    }

    /// <summary>
    /// Get all registered services (for debugging)
    /// </summary>
    public Dictionary<Type, Component> GetAllServices()
    {
        return new Dictionary<Type, Component>(_services);
    }

    #region Editor Utilities
#if UNITY_EDITOR
    [ContextMenu("List Registered Services")]
    private void ListRegisteredServices()
    {
        Debug.Log($"[SceneServiceLocator] Registered services in {gameObject.scene.name}:");
        foreach (var kvp in _services)
        {
            Debug.Log($"  - {kvp.Key.Name}: {kvp.Value.name}");
        }
    }

    [ContextMenu("Force Auto-Register")]
    private void ForceAutoRegister()
    {
        _services.Clear();
        AutoRegisterServices();
        Debug.Log("[SceneServiceLocator] Forced auto-registration complete");
    }
#endif
    #endregion
}
