using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using Debug = UnityEngine.Debug;

namespace Stylo.MenUI.UI
{
    /// <summary>
    /// Proper UI highlighting system that uses Unity's built-in selection colors
    /// and visual states instead of problematic overlays.
    /// </summary>
    public class MenUISelectionHighlighter : MonoBehaviour
    {
        [Header("Highlight Colors")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color highlightedColor = new Color(0.9f, 0.9f, 0.9f, 1f);
        [SerializeField] private Color selectedColor = new Color(0.8f, 0.8f, 1f, 1f);
        [SerializeField] private Color pressedColor = new Color(0.7f, 0.7f, 0.7f, 1f);
        [SerializeField] private Color disabledColor = new Color(0.5f, 0.5f, 0.5f, 0.5f);

        [Header("Selection Indicator")]
        [SerializeField] private bool useSelectionIndicator = true;
        [SerializeField] private Color indicatorColor = new Color(0.2f, 0.6f, 1f, 1f);
        [SerializeField] private float indicatorWidth = 4f;

        [Header("Text Highlighting")]
        [SerializeField] private bool highlightText = true;
        [SerializeField] private Color textHighlightColor = new Color(1f, 1f, 0.8f, 1f);

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogging = false;

        private EventSystem _eventSystem;
        private GameObject _lastSelectedObject;

        private void Start()
        {
            _eventSystem = EventSystem.current;
            SetupUIHighlighting();
        }

        private void Update()
        {
            CheckSelectionChange();
        }

        /// <summary>
        /// Setup proper highlighting for all UI elements
        /// </summary>
        private void SetupUIHighlighting()
        {
            var selectables = FindObjectsOfType<Selectable>();

            foreach (var selectable in selectables)
            {
                SetupSelectableColors(selectable);
            }

            if (enableDebugLogging)
                Debug.Log($"[MenUISelectionHighlighter] Setup highlighting for {selectables.Length} UI elements");
        }

        /// <summary>
        /// Setup proper color transitions for a selectable UI element
        /// </summary>
        /// <param name="selectable">The selectable to setup</param>
        private void SetupSelectableColors(Selectable selectable)
        {
            if (selectable == null) return;

            var colors = selectable.colors;
            colors.normalColor = normalColor;
            colors.highlightedColor = highlightedColor;
            colors.selectedColor = selectedColor;
            colors.pressedColor = pressedColor;
            colors.disabledColor = disabledColor;
            colors.colorMultiplier = 1f;
            colors.fadeDuration = 0.1f;

            selectable.colors = colors;

            // Ensure the selectable uses color transitions
            selectable.transition = Selectable.Transition.ColorTint;

            // Add selection indicator if enabled
            if (useSelectionIndicator)
            {
                AddSelectionIndicator(selectable);
            }
        }

        /// <summary>
        /// Add a subtle selection indicator (outline) to UI elements
        /// </summary>
        /// <param name="selectable">The selectable to add indicator to</param>
        private void AddSelectionIndicator(Selectable selectable)
        {
            var outline = selectable.GetComponent<Outline>();
            if (outline == null)
            {
                outline = selectable.gameObject.AddComponent<Outline>();
            }

            outline.effectColor = indicatorColor;
            outline.effectDistance = new Vector2(indicatorWidth, indicatorWidth);
            outline.useGraphicAlpha = false;
            outline.enabled = false; // Start disabled, enable when selected
        }

        /// <summary>
        /// Check if the selected UI element has changed
        /// </summary>
        private void CheckSelectionChange()
        {
            if (_eventSystem == null) return;

            var currentSelected = _eventSystem.currentSelectedGameObject;

            if (currentSelected != _lastSelectedObject)
            {
                // Remove highlight from previous selection
                if (_lastSelectedObject != null)
                {
                    RemoveSelectionHighlight(_lastSelectedObject);
                }

                // Add highlight to new selection
                if (currentSelected != null)
                {
                    AddSelectionHighlight(currentSelected);
                }

                _lastSelectedObject = currentSelected;

                if (enableDebugLogging && currentSelected != null)
                    Debug.Log($"[MenUISelectionHighlighter] Selected: {currentSelected.name}");
            }
        }

        /// <summary>
        /// Add visual highlight to the selected element
        /// </summary>
        /// <param name="selectedObject">The selected UI element</param>
        private void AddSelectionHighlight(GameObject selectedObject)
        {
            if (selectedObject == null) return;

            // Enable outline indicator
            if (useSelectionIndicator)
            {
                var outline = selectedObject.GetComponent<Outline>();
                if (outline != null)
                {
                    outline.enabled = true;
                }
            }

            // Highlight text if enabled
            if (highlightText)
            {
                HighlightText(selectedObject, true);
            }
        }

        /// <summary>
        /// Remove visual highlight from the deselected element
        /// </summary>
        /// <param name="deselectedObject">The deselected UI element</param>
        private void RemoveSelectionHighlight(GameObject deselectedObject)
        {
            if (deselectedObject == null) return;

            // Disable outline indicator
            if (useSelectionIndicator)
            {
                var outline = deselectedObject.GetComponent<Outline>();
                if (outline != null)
                {
                    outline.enabled = false;
                }
            }

            // Remove text highlighting
            if (highlightText)
            {
                HighlightText(deselectedObject, false);
            }
        }

        /// <summary>
        /// Highlight or unhighlight text elements
        /// </summary>
        /// <param name="uiElement">The UI element to process</param>
        /// <param name="highlight">Whether to highlight or unhighlight</param>
        private void HighlightText(GameObject uiElement, bool highlight)
        {
            // Handle TextMeshPro components
            var tmpTexts = uiElement.GetComponentsInChildren<TextMeshProUGUI>();
            foreach (var tmpText in tmpTexts)
            {
                if (highlight)
                {
                    // Store original color if not already stored
                    if (!tmpText.gameObject.TryGetComponent<OriginalTextColor>(out var originalColor))
                    {
                        originalColor = tmpText.gameObject.AddComponent<OriginalTextColor>();
                        originalColor.color = tmpText.color;
                    }
                    tmpText.color = textHighlightColor;
                }
                else
                {
                    // Restore original color
                    if (tmpText.gameObject.TryGetComponent<OriginalTextColor>(out var originalColor))
                    {
                        tmpText.color = originalColor.color;
                    }
                }
            }

            // Handle regular Text components
            var texts = uiElement.GetComponentsInChildren<Text>();
            foreach (var text in texts)
            {
                if (highlight)
                {
                    // Store original color if not already stored
                    if (!text.gameObject.TryGetComponent<OriginalTextColor>(out var originalColor))
                    {
                        originalColor = text.gameObject.AddComponent<OriginalTextColor>();
                        originalColor.color = text.color;
                    }
                    text.color = textHighlightColor;
                }
                else
                {
                    // Restore original color
                    if (text.gameObject.TryGetComponent<OriginalTextColor>(out var originalColor))
                    {
                        text.color = originalColor.color;
                    }
                }
            }
        }

        /// <summary>
        /// Refresh highlighting setup for all UI elements
        /// </summary>
        [ContextMenu("Refresh UI Highlighting")]
        public void RefreshHighlighting()
        {
            SetupUIHighlighting();
        }

        /// <summary>
        /// Set custom highlight colors
        /// </summary>
        /// <param name="normal">Normal state color</param>
        /// <param name="highlighted">Highlighted state color</param>
        /// <param name="selected">Selected state color</param>
        public void SetHighlightColors(Color normal, Color highlighted, Color selected)
        {
            normalColor = normal;
            highlightedColor = highlighted;
            selectedColor = selected;
            RefreshHighlighting();
        }

        /// <summary>
        /// Enable or disable selection indicators
        /// </summary>
        /// <param name="enabled">Whether selection indicators should be enabled</param>
        public void SetSelectionIndicators(bool enabled)
        {
            useSelectionIndicator = enabled;
            RefreshHighlighting();
        }

        private void OnDestroy()
        {
            // Clean up any remaining highlights
            if (_lastSelectedObject != null)
            {
                RemoveSelectionHighlight(_lastSelectedObject);
            }
        }
    }

    /// <summary>
    /// Helper component to store original text colors
    /// </summary>
    public class OriginalTextColor : MonoBehaviour
    {
        public Color color;
    }
}
