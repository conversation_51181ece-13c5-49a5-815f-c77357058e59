using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple bridge for loading Last UI settings scene when needed.
    /// Handles basic scene loading/unloading for MenUI integration.
    /// </summary>
    public class LastUISceneBridge : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private string lastUISceneName = "demo";
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool hidePauseMenuWhenSettingsOpen = false;

        // State tracking
        private bool _lastUISceneLoaded = false;
        private SimpleMenUISystem _menUISystem;

        private void Awake()
        {
            _menUISystem = GetComponent<SimpleMenUISystem>();
            if (_menUISystem == null)
            {
                Debug.LogError("[LastUISceneBridge] Must be attached to GameObject with SimpleMenUISystem component");
                enabled = false;
            }
        }

        private void OnDestroy()
        {
            // Cleanup any loaded scenes
            if (_lastUISceneLoaded)
            {
                SceneManager.UnloadSceneAsync(lastUISceneName);
            }
        }

        /// <summary>
        /// Load Last UI settings scene additively
        /// </summary>
        public async Task<bool> LoadLastUISettingsScene()
        {
            if (_lastUISceneLoaded)
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUISceneBridge] Last UI scene already loaded");
                return true;
            }

            try
            {
                if (enableDebugLogging)
                    Debug.Log($"[LastUISceneBridge] Loading Last UI scene: {lastUISceneName}");

                // Load scene additively
                var asyncLoad = SceneManager.LoadSceneAsync(lastUISceneName, LoadSceneMode.Additive);

                // Wait for scene to load
                while (!asyncLoad.isDone)
                {
                    await Task.Yield();
                }

                _lastUISceneLoaded = true;

                // Hide pause menu if configured (default: keep pause menu visible)
                if (hidePauseMenuWhenSettingsOpen && _menUISystem != null)
                {
                    _menUISystem.HidePauseMenuOnly(); // Hide menu but keep game paused
                    if (enableDebugLogging)
                        Debug.Log("[LastUISceneBridge] Pause menu hidden while settings are open (game remains paused)");
                }
                else if (enableDebugLogging)
                {
                    Debug.Log("[LastUISceneBridge] Pause menu remains visible (overlay mode)");
                }

                // Setup exit listener for Last UI
                SetupLastUIExitListener();

                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Last UI scene loaded successfully");

                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[LastUISceneBridge] Failed to load Last UI scene: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unload Last UI scene and return to MenUI
        /// </summary>
        public async Task<bool> UnloadLastUIScene()
        {
            if (!_lastUISceneLoaded)
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUISceneBridge] Last UI scene not loaded, cannot unload");
                return true;
            }

            try
            {
                if (enableDebugLogging)
                    Debug.Log($"[LastUISceneBridge] Unloading Last UI scene: {lastUISceneName}");

                // Unload scene
                var asyncUnload = SceneManager.UnloadSceneAsync(lastUISceneName);
                while (!asyncUnload.isDone)
                {
                    await Task.Yield();
                }

                _lastUISceneLoaded = false;

                // Show pause menu again if it was hidden
                if (hidePauseMenuWhenSettingsOpen && _menUISystem != null)
                {
                    _menUISystem.ShowPauseMenuOnly(); // Show menu without changing pause state
                    if (enableDebugLogging)
                        Debug.Log("[LastUISceneBridge] Pause menu restored after settings closed (game remains paused)");
                }
                else if (enableDebugLogging)
                {
                    Debug.Log("[LastUISceneBridge] Pause menu was never hidden (overlay mode)");
                }

                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Last UI scene unloaded successfully");

                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[LastUISceneBridge] Failed to unload Last UI scene: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if Last UI scene is currently loaded
        /// </summary>
        public bool IsLastUISceneLoaded => _lastUISceneLoaded;

        /// <summary>
        /// Get the configured Last UI scene name
        /// </summary>
        public string GetLastUISceneName() => lastUISceneName;

        /// <summary>
        /// Setup listener for Last UI exit events
        /// </summary>
        private void SetupLastUIExitListener()
        {
            // Find the Last UI input manager and listen for back/cancel events
            var lastUIInputManager = FindFirstObjectByType<UIInputManager>();
            if (lastUIInputManager != null)
            {
                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Found UIInputManager, setting up exit listener");

                // The UIInputManager already handles cancel/back input
                // We just need to ensure it can call back to us when needed
            }
            else if (enableDebugLogging)
            {
                Debug.LogWarning("[LastUISceneBridge] UIInputManager not found in Last UI scene");
            }

            // Also setup a direct bridge component in the Last UI scene for proper input handling
            SetupLastUIBridgeComponent();
        }

        /// <summary>
        /// Setup the bridge component in the Last UI scene for proper input handling
        /// </summary>
        private void SetupLastUIBridgeComponent()
        {
            // Find or create the bridge component in the Last UI scene
            var existingBridge = FindFirstObjectByType<LastUIInputBridge>();
            if (existingBridge == null)
            {
                var bridgeGO = new GameObject("LastUIInputBridge");
                var bridge = bridgeGO.AddComponent<LastUIInputBridge>();
                bridge.Initialize(this);

                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Created LastUIInputBridge component in Last UI scene");
            }
            else
            {
                existingBridge.Initialize(this);
                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Found existing LastUIInputBridge, re-initialized");
            }
        }

        /// <summary>
        /// Called by Last UI when user wants to exit settings
        /// </summary>
        public async void OnLastUIExitRequested()
        {
            if (enableDebugLogging)
                Debug.Log("[LastUISceneBridge] Last UI exit requested, unloading scene");

            await UnloadLastUIScene();
        }

        /// <summary>
        /// Force unload Last UI scene (for emergency cleanup)
        /// </summary>
        public async Task<bool> ForceUnloadLastUIScene()
        {
            if (_lastUISceneLoaded)
            {
                if (enableDebugLogging)
                    Debug.Log("[LastUISceneBridge] Force unloading Last UI scene");

                try
                {
                    var asyncUnload = SceneManager.UnloadSceneAsync(lastUISceneName);
                    while (!asyncUnload.isDone)
                    {
                        await Task.Yield();
                    }
                    _lastUISceneLoaded = false;

                    // Show pause menu again
                    if (hidePauseMenuWhenSettingsOpen && _menUISystem != null)
                    {
                        _menUISystem.ShowPauseMenuOnly(); // Show menu without changing pause state
                    }

                    return true;
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[LastUISceneBridge] Failed to force unload: {e.Message}");
                    return false;
                }
            }
            return true;
        }

    }
}
