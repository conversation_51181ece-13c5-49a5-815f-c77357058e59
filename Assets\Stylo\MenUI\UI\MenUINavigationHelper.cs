using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Debug = UnityEngine.Debug;

namespace Stylo.MenUI.UI
{
    /// <summary>
    /// Helper component that automatically sets up controller navigation for MenUI panels.
    /// Attach this to settings panels to ensure proper navigation flow.
    /// </summary>
    public class MenUINavigationHelper : MonoBehaviour
    {
        [Header("Navigation Settings")]
        [SerializeField] private bool autoSetupOnEnable = true;
        [SerializeField] private bool debugLogging = false;

        [Header("Navigation Overrides")]
        [SerializeField] private Selectable firstSelectable;
        [SerializeField] private Selectable lastSelectable;

        [Header("External Navigation")]
        [SerializeField] private Selectable leftNavigationTarget;
        [SerializeField] private Selectable rightNavigationTarget;
        [SerializeField] private Selectable upNavigationTarget;
        [SerializeField] private Selectable downNavigationTarget;

        private List<Selectable> _selectables = new List<Selectable>();
        private bool _isSetup = false;

        private void OnEnable()
        {
            if (autoSetupOnEnable)
            {
                // Delay setup to ensure all UI elements are properly initialized
                StartCoroutine(DelayedSetup());
            }
        }

        private System.Collections.IEnumerator DelayedSetup()
        {
            // Wait one frame to ensure all UI elements are active and initialized
            yield return null;
            SetupNavigation();
        }

        /// <summary>
        /// Manually setup navigation for this panel
        /// </summary>
        [ContextMenu("Setup Navigation")]
        public void SetupNavigation()
        {
            if (_isSetup) return;

            CollectSelectables();
            SetupVerticalNavigation();
            SetupExternalNavigation();

            _isSetup = true;

            if (debugLogging)
                Debug.Log($"[MenUINavigationHelper] Navigation setup complete for {gameObject.name} with {_selectables.Count} selectables");
        }

        /// <summary>
        /// Force refresh navigation setup
        /// </summary>
        public void RefreshNavigation()
        {
            _isSetup = false;
            SetupNavigation();
        }

        /// <summary>
        /// Get the first selectable element in this panel
        /// </summary>
        /// <returns>The first selectable element, or null if none found</returns>
        public Selectable GetFirstSelectable()
        {
            if (!_isSetup) SetupNavigation();

            if (firstSelectable != null) return firstSelectable;
            return _selectables.Count > 0 ? _selectables[0] : null;
        }

        /// <summary>
        /// Get the last selectable element in this panel
        /// </summary>
        /// <returns>The last selectable element, or null if none found</returns>
        public Selectable GetLastSelectable()
        {
            if (!_isSetup) SetupNavigation();

            if (lastSelectable != null) return lastSelectable;
            return _selectables.Count > 0 ? _selectables[_selectables.Count - 1] : null;
        }

        /// <summary>
        /// Select the first interactable element in this panel
        /// </summary>
        public void SelectFirst()
        {
            var first = GetFirstSelectable();
            if (first != null && first.interactable)
            {
                // Clear any existing selection first
                UnityEngine.EventSystems.EventSystem.current.SetSelectedGameObject(null);

                // Wait a frame then select to ensure proper highlighting
                StartCoroutine(SelectAfterFrame(first));

                if (debugLogging)
                    Debug.Log($"[MenUINavigationHelper] Selected first element: {first.name}");
            }
        }

        /// <summary>
        /// Select a UI element after waiting one frame
        /// </summary>
        private System.Collections.IEnumerator SelectAfterFrame(Selectable selectable)
        {
            yield return null; // Wait one frame
            if (selectable != null && selectable.gameObject.activeInHierarchy && selectable.interactable)
            {
                selectable.Select();

                if (debugLogging)
                    Debug.Log($"[MenUINavigationHelper] Successfully selected {selectable.name}");
            }
        }

        /// <summary>
        /// Collect all selectable elements in this panel
        /// </summary>
        private void CollectSelectables()
        {
            _selectables.Clear();

            // Get all selectables in children (excluding inactive ones)
            var allSelectables = GetComponentsInChildren<Selectable>(false);

            foreach (var selectable in allSelectables)
            {
                if (selectable.interactable && selectable.gameObject.activeInHierarchy)
                {
                    _selectables.Add(selectable);
                }
            }

            // Sort by hierarchy order (top to bottom)
            _selectables.Sort((a, b) =>
            {
                var aTransform = a.transform;
                var bTransform = b.transform;

                // If they have the same parent, compare sibling index
                if (aTransform.parent == bTransform.parent)
                {
                    return aTransform.GetSiblingIndex().CompareTo(bTransform.GetSiblingIndex());
                }

                // Otherwise compare by Y position (higher Y = earlier in list)
                return bTransform.position.y.CompareTo(aTransform.position.y);
            });
        }

        /// <summary>
        /// Setup vertical navigation between elements in this panel
        /// </summary>
        private void SetupVerticalNavigation()
        {
            for (int i = 0; i < _selectables.Count; i++)
            {
                var current = _selectables[i];
                var nav = current.navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up/down navigation
                if (i > 0)
                    nav.selectOnUp = _selectables[i - 1];

                if (i < _selectables.Count - 1)
                    nav.selectOnDown = _selectables[i + 1];

                current.navigation = nav;
            }
        }

        /// <summary>
        /// Setup navigation to external elements (like category buttons)
        /// </summary>
        private void SetupExternalNavigation()
        {
            if (_selectables.Count == 0) return;

            // Setup left navigation for first element
            if (leftNavigationTarget != null)
            {
                var firstNav = _selectables[0].navigation;
                firstNav.selectOnLeft = leftNavigationTarget;
                _selectables[0].navigation = firstNav;

                // Also setup the reverse navigation
                var leftNav = leftNavigationTarget.navigation;
                leftNav.mode = Navigation.Mode.Explicit;
                leftNav.selectOnRight = _selectables[0];
                leftNavigationTarget.navigation = leftNav;
            }

            // Setup right navigation for last element
            if (rightNavigationTarget != null)
            {
                var lastNav = _selectables[_selectables.Count - 1].navigation;
                lastNav.selectOnRight = rightNavigationTarget;
                _selectables[_selectables.Count - 1].navigation = lastNav;
            }

            // Setup up navigation for first element
            if (upNavigationTarget != null)
            {
                var firstNav = _selectables[0].navigation;
                firstNav.selectOnUp = upNavigationTarget;
                _selectables[0].navigation = firstNav;
            }

            // Setup down navigation for last element
            if (downNavigationTarget != null)
            {
                var lastNav = _selectables[_selectables.Count - 1].navigation;
                lastNav.selectOnDown = downNavigationTarget;
                _selectables[_selectables.Count - 1].navigation = lastNav;
            }
        }

        /// <summary>
        /// Set the left navigation target (usually a category button)
        /// </summary>
        /// <param name="target">The selectable to navigate to when pressing left</param>
        public void SetLeftNavigationTarget(Selectable target)
        {
            leftNavigationTarget = target;
            if (_isSetup) RefreshNavigation();
        }

        /// <summary>
        /// Set the right navigation target
        /// </summary>
        /// <param name="target">The selectable to navigate to when pressing right</param>
        public void SetRightNavigationTarget(Selectable target)
        {
            rightNavigationTarget = target;
            if (_isSetup) RefreshNavigation();
        }

        private void OnDisable()
        {
            _isSetup = false;
        }

        private void OnValidate()
        {
            if (Application.isPlaying && _isSetup)
            {
                RefreshNavigation();
            }
        }
    }
}
